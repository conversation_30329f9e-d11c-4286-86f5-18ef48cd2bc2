in KuruForwarder.sol  

Price trigger logic is inverted (executes on the wrong side)

Where: executePriceDependent

require(
  (req.isBelowPrice && req.price < _currentBidPrice) ||
  (!req.isBelowPrice && req.price > _currentBidPrice),
  PriceDependentRequestFailed(_currentBidPrice, req.price)
);


Why:

If the intent is “execute when current price ≤ threshold when isBelowPrice==true and ≥ threshold when false”, the checks are flipped.

Example: User wants execute when price ≤ 100 (isBelowPrice=true). If the current bid is 150, the code reads 100 < 150 → executes, which is the opposite of intent.

Also uses strict </> (excludes equality), which can miss edge triggers.

Impact: Wrong-side execution of user orders; can cause immediate bad fills / loss.

If isBelowPrice == true, it executes when trigger < current, i.e., when price is above the threshold (wrong side).

If isBelowPrice == false, it executes when trigger > current, i.e., when price is below the threshold (wrong side).

It’s also strict (< / >) so equality at the threshold never fires.

Expected semantics

Most UIs/users expect:

isBelowPrice == true → execute when current ≤ trigger.

isBelowPrice == false → execute when current ≥ trigger.

Minimal repro (thought experiment)

User sets price=100, isBelowPrice=true (“run when we drop to 100 or lower”).

Order book returns _currentBidPrice = 150.

Current code checks 100 < 150 → passes and executes (incorrect).


